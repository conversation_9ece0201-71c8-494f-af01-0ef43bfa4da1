Stack trace:
Frame         Function      Args
0007FFFFBF80  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFBF80, 0007FFFFAE80) msys-2.0.dll+0x2118E
0007FFFFBF80  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFC258) msys-2.0.dll+0x69BA
0007FFFFBF80  0002100469F2 (00021028DF99, 0007FFFFBE38, 0007FFFFBF80, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBF80  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBF80  00021006A545 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFC260  00021006B9A5 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA8F240000 ntdll.dll
7FFA8E0C0000 KERNEL32.DLL
7FFA8C580000 KERNELBASE.dll
7FFA8ECC0000 USER32.dll
7FFA8CE80000 win32u.dll
7FFA8E190000 GDI32.dll
7FFA8C390000 gdi32full.dll
7FFA8C4D0000 msvcp_win.dll
7FFA8CA30000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA8D720000 advapi32.dll
7FFA8EB60000 msvcrt.dll
7FFA8EAB0000 sechost.dll
7FFA8EFD0000 RPCRT4.dll
7FFA8B8A0000 CRYPTBASE.DLL
7FFA8CEB0000 bcryptPrimitives.dll
7FFA8EC80000 IMM32.DLL
