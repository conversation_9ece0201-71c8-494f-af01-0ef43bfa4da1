#!/usr/bin/env python3
"""
Quick test to check database compliance_assessment values
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from data_ops import get_db_connection
    import psycopg2
    
    def check_compliance_data():
        """Check compliance_assessment data in database"""
        print("🔍 Checking compliance_assessment data")
        print("=" * 40)
        
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # Check total records with need_review = true
            cursor.execute("SELECT COUNT(*) FROM results WHERE need_review = true")
            total_need_review = cursor.fetchone()[0]
            print(f"Total records with need_review = true: {total_need_review}")
            
            # Check compliance_assessment distribution
            cursor.execute("""
                SELECT compliance_assessment, COUNT(*) 
                FROM results 
                WHERE need_review = true 
                GROUP BY compliance_assessment
                ORDER BY compliance_assessment
            """)
            
            compliance_stats = cursor.fetchall()
            print(f"\nCompliance assessment distribution:")
            for compliance_val, count in compliance_stats:
                val_type = type(compliance_val).__name__
                print(f"  {compliance_val} ({val_type}): {count} records")
            
            # Show sample records
            cursor.execute("""
                SELECT image_name, compliance_assessment, need_review
                FROM results 
                WHERE need_review = true 
                LIMIT 10
            """)
            
            sample_records = cursor.fetchall()
            print(f"\nSample records:")
            for i, (name, compliance, need_review) in enumerate(sample_records, 1):
                comp_type = type(compliance).__name__
                print(f"  {i}. {name}: compliance={compliance} ({comp_type}), need_review={need_review}")
            
            cursor.close()
            conn.close()
            
            # Test the counting logic
            print(f"\nTesting counting logic:")
            test_data = [
                {'compliance_assessment': True},
                {'compliance_assessment': False},
                {'compliance_assessment': None},
                {'compliance_assessment': True},
                {'compliance_assessment': False},
            ]
            
            pass_count = len([item for item in test_data if item.get('compliance_assessment') is True])
            fail_count = len([item for item in test_data if item.get('compliance_assessment') is False])
            
            print(f"  Test data: {test_data}")
            print(f"  Pass count (is True): {pass_count}")
            print(f"  Fail count (is False): {fail_count}")
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            import traceback
            traceback.print_exc()
    
    if __name__ == "__main__":
        check_compliance_data()
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Make sure psycopg2 and other dependencies are installed")
except Exception as e:
    print(f"❌ General error: {e}")
    import traceback
    traceback.print_exc()
